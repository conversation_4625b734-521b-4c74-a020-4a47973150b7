import os
import re

log_dir = 'api_logs'
# 匹配以139开头，49结尾，11位的手机号
pattern = re.compile(r'183\d{6}64')

lines_found = []

for root, dirs, files in os.walk(log_dir):
    for file in files:
        file_path = os.path.join(root, file)
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    # 查找所有匹配的手机号
                    matches = pattern.findall(line)
                    lines_found.extend(matches)
        except Exception as e:
            pass  # 忽略无法读取的文件

with open('sjh.txt', 'w', encoding='utf-8') as out:
    for line in lines_found:
        out.write(line + '\n')
