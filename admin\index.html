<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理后台</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛡️</text></svg>">
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    
    <!-- Chart.js for statistics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Axios for HTTP requests -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .admin-container {
            min-height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        .header .el-menu {
            background: transparent !important;
            border: none !important;
        }
        
        .header .el-menu-item {
            color: rgba(255, 255, 255, 0.8) !important;
            border-bottom: 2px solid transparent !important;
        }
        
        .header .el-menu-item:hover,
        .header .el-menu-item.is-active {
            color: white !important;
            background: rgba(255, 255, 255, 0.1) !important;
            border-bottom-color: white !important;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            color: white;
            margin-right: 50px;
        }
        
        .logo i {
            margin-right: 10px;
            font-size: 24px;
        }
        
        .main-content {
            padding: 20px;
            min-height: calc(100vh - 60px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }
        
        .stat-card .icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 16px;
        }
        
        .stat-card .value {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .stat-card .label {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .content-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .content-card .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
        }
        
        .content-card .card-body {
            padding: 24px;
        }
        
        .search-bar {
            margin-bottom: 20px;
        }
        
        .action-buttons {
            margin-bottom: 20px;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .el-table {
            border-radius: 8px;
        }
        
        .el-table th {
            background: #f8f9fa !important;
            color: #495057 !important;
            font-weight: 600 !important;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-banned {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-vip {
            background: #fff3cd;
            color: #856404;
        }
        
        .pagination-container {
            margin-top: 20px;
            text-align: center;
        }
        
        .loading-container {
            text-align: center;
            padding: 50px;
        }
        
        .empty-container {
            text-align: center;
            padding: 50px;
            color: #999;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 0 10px;
            }
            
            .main-content {
                padding: 10px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .stat-card {
                padding: 20px;
            }
            
            .content-card .card-body {
                padding: 15px;
            }
        }
        
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.3s ease;
        }
        
        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
        
        .slide-enter-active, .slide-leave-active {
            transition: all 0.3s ease;
        }
        
        .slide-enter-from {
            transform: translateX(30px);
            opacity: 0;
        }
        
        .slide-leave-to {
            transform: translateX(-30px);
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="admin-container">
            <!-- Header -->
            <div class="header">
                <el-row align="middle" style="height: 60px;">
                    <el-col :span="6">
                        <div class="logo">
                            <el-icon><Shield /></el-icon>
                            系统管理后台
                        </div>
                    </el-col>
                    <el-col :span="12">
                        <el-menu 
                            :default-active="activeMenu" 
                            mode="horizontal" 
                            @select="handleMenuSelect"
                            style="border: none;">
                            <el-menu-item index="dashboard">
                                <el-icon><Odometer /></el-icon>
                                <span>仪表盘</span>
                            </el-menu-item>
                            <el-menu-item index="users">
                                <el-icon><User /></el-icon>
                                <span>用户管理</span>
                            </el-menu-item>
                            <el-menu-item index="cards">
                                <el-icon><CreditCard /></el-icon>
                                <span>卡密管理</span>
                            </el-menu-item>
                            <el-menu-item index="system">
                                <el-icon><Setting /></el-icon>
                                <span>系统配置</span>
                            </el-menu-item>
                            <el-menu-item index="logs">
                                <el-icon><Document /></el-icon>
                                <span>日志管理</span>
                            </el-menu-item>
                        </el-menu>
                    </el-col>
                    <el-col :span="6" style="text-align: right;">
                        <el-dropdown>
                            <span style="color: white; cursor: pointer;">
                                <el-icon><UserFilled /></el-icon>
                                管理员
                                <el-icon><ArrowDown /></el-icon>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="logout">
                                        <el-icon><SwitchButton /></el-icon>
                                        退出登录
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </el-col>
                </el-row>
            </div>
            
            <!-- Main Content -->
            <div class="main-content">
                <transition name="fade" mode="out-in">
                    <!-- Dashboard -->
                    <div v-if="activeMenu === 'dashboard'" key="dashboard">
                        <h2 style="margin-bottom: 20px; color: #2c3e50;">仪表盘</h2>
                        
                        <!-- Statistics Cards -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                    <el-icon><User /></el-icon>
                                </div>
                                <div class="value">{{ stats.totalUsers }}</div>
                                <div class="label">总用户数</div>
                            </div>
                            <div class="stat-card">
                                <div class="icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                                    <el-icon><Star /></el-icon>
                                </div>
                                <div class="value">{{ stats.vipUsers }}</div>
                                <div class="label">VIP用户</div>
                            </div>
                            <div class="stat-card">
                                <div class="icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                    <el-icon><Odometer /></el-icon>
                                </div>
                                <div class="value">{{ stats.todayLaunches }}</div>
                                <div class="label">今日启动</div>
                            </div>
                            <div class="stat-card">
                                <div class="icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                                    <el-icon><CreditCard /></el-icon>
                                </div>
                                <div class="value">{{ stats.availableCards }}</div>
                                <div class="label">可用卡密</div>
                            </div>
                        </div>
                        
                        <!-- Charts -->
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="content-card">
                                    <div class="card-header">
                                        <h3>启动统计</h3>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="launchChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="content-card">
                                    <div class="card-header">
                                        <h3>注册统计</h3>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="registerChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </transition>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
