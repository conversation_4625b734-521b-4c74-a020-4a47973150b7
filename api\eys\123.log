http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=******************
请求参数: {"name":"\u5f20\u4e09","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%B9%B3&idcard=******************
请求参数: {"name":"\u6768\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=110101199001011234请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=110101199001011234
请求参数: {"name":"\u5f20\u4e09","idcard":"110101199001011234"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************
请求参数: {"name":"\u8def\u751c\u751c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************
请求参数: {"name":"\u674e\u5b50\u9f50","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E5%AD%90%E9%BD%90&idcard=******************
请求参数: {"name":"\u674e\u5b50\u9f50","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E9%9D%99&idcard=150702198808082150请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E9%9D%99&idcard=150702198808082150
请求参数: {"name":"\u9648\u9759","idcard":"150702198808082150"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070920请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070920
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070920"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E6%83%A0%E5%B9%B3&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E6%83%A0%E5%B9%B3&idcard=******************
请求参数: {"name":"\u5f20\u60e0\u5e73","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070927
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070927"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9E%97%E7%82%9C%E7%8E%A5&idcard=******************
请求参数: {"name":"\u6797\u709c\u73a5","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070925请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E4%B8%BD%E7%8F%8D&idcard=360733199310070925
请求参数: {"name":"\u5218\u4e3d\u73cd","idcard":"360733199310070925"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%8D%9A%E5%AE%8F&idcard=350128201109050013请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E5%8D%9A%E5%AE%8F&idcard=350128201109050013
请求参数: {"name":"\u9648\u535a\u5b8f","idcard":"350128201109050013"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%94%90%E7%AB%8B%E7%BA%A2&idcard=341126199106232520请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%94%90%E7%AB%8B%E7%BA%A2&idcard=341126199106232520
请求参数: {"name":"\u5510\u7acb\u7ea2","idcard":"341126199106232520"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%A2%93%E5%87%AF&idcard=510681201101173018请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E6%A2%93%E5%87%AF&idcard=510681201101173018
请求参数: {"name":"\u5218\u6893\u51ef","idcard":"510681201101173018"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E6%B0%B4%E4%BB%99&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E6%B0%B4%E4%BB%99&idcard=******************
请求参数: {"name":"\u9ec4\u6c34\u4ed9","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B5%E9%95%BF%E7%94%9F&idcard=360602195301181512请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%82%B5%E9%95%BF%E7%94%9F&idcard=360602195301181512
请求参数: {"name":"\u90b5\u957f\u751f","idcard":"360602195301181512"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E8%87%AA%E9%BE%99&idcard=360602195301181512请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E8%87%AA%E9%BE%99&idcard=360602195301181512
请求参数: {"name":"\u738b\u81ea\u9f99","idcard":"360602195301181512"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E6%AC%A3%E4%BB%81&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%9F%A6%E6%AC%A3%E4%BB%81&idcard=******************
请求参数: {"name":"\u97e6\u6b23\u4ec1","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%B2%A9&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%B2%A9&idcard=******************
请求参数: {"name":"\u9ad8\u5ca9","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%86%E8%AF%97%E9%9F%B5&idcard=310106200505201629请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%86%E8%AF%97%E9%9F%B5&idcard=310106200505201629
请求参数: {"name":"\u9646\u8bd7\u97f5","idcard":"310106200505201629"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%A6%86%E5%87%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%A6%86%E5%87%AF&idcard=******************
请求参数: {"name":"\u5468\u6986\u51ef","idcard":"******************"}
响应数据: {"message":"操作成功","result":true,"ishave":0}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%AE%9A%E7%83%A8&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%AE%9A%E7%83%A8&idcard=******************
请求参数: {"name":"\u6768\u5b9a\u70e8","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427
请求参数: {"name":"\u9ad8\u5b50\u6dc7","idcard":"320321201102111427"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%AB%98%E5%AD%90%E6%B7%87&idcard=320321201102111427
请求参数: {"name":"\u9ad8\u5b50\u6dc7","idcard":"320321201102111427"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%89%BA%E6%B8%B2&idcard=36062220120206391X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%89%BA%E6%B8%B2&idcard=36062220120206391X
请求参数: {"name":"\u5f20\u827a\u6e32","idcard":"36062220120206391X"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%90%91%E9%AB%98&idcard=620702201210237813请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%94%A1%E5%90%91%E9%AB%98&idcard=620702201210237813
请求参数: {"name":"\u8521\u5411\u9ad8","idcard":"620702201210237813"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************
请求参数: {"name":"\u5468\u67ef\u5b87","idcard":"******************"}
响应数据: 

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E6%9F%AF%E5%AE%87&idcard=******************
请求参数: {"name":"\u5468\u67ef\u5b87","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BD%B3%E6%80%A1&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E4%BD%B3%E6%80%A1&idcard=******************
请求参数: {"name":"\u674e\u4f73\u6021","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%B7%83%E8%99%8E&idcard=211322197711125317请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E8%B7%83%E8%99%8E&idcard=211322197711125317
请求参数: {"name":"\u5f20\u8dc3\u864e","idcard":"211322197711125317"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AE%87%E7%BA%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%AE%87%E7%BA%AF&idcard=******************
请求参数: {"name":"\u5218\u5b87\u7eaf","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%A4%A7%E6%A0%B9&idcard=130427197908254717请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%A8%E5%A4%A7%E6%A0%B9&idcard=130427197908254717
请求参数: {"name":"\u6768\u5927\u6839","idcard":"130427197908254717"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804
请求参数: {"name":"\u65bd\u5947","idcard":"130227198306108804"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804
请求参数: {"name":"\u65bd\u5947","idcard":"130227198306108804"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%96%BD%E5%A5%87&idcard=130227198306108804
请求参数: {"name":"\u65bd\u5947","idcard":"130227198306108804"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181
请求参数: {"name":"\u66f2\u51a0","idcard":"130284196102044181"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9B%B2%E5%86%A0&idcard=130284196102044181
请求参数: {"name":"\u66f2\u51a0","idcard":"130284196102044181"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%8F%8B%E8%89%BA&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E5%8F%8B%E8%89%BA&idcard=******************
请求参数: {"name":"\u5f20\u53cb\u827a","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%82%96%E8%8A%B8&idcard=511322200810274882请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%82%96%E8%8A%B8&idcard=511322200810274882
请求参数: {"name":"\u8096\u82b8","idcard":"511322200810274882"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E5%BC%88%E5%8D%9A&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E5%BC%88%E5%8D%9A&idcard=******************
请求参数: {"name":"\u8463\u5f08\u535a","idcard":"******************"}
响应数据: {"message":"在线办证需读者年满16周岁，请您重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%9F%8F%E5%AE%87&idcard=330226200510247036请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%99%88%E6%9F%8F%E5%AE%87&idcard=330226200510247036
请求参数: {"name":"\u9648\u67cf\u5b87","idcard":"330226200510247036"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%BB%B6%E8%8A%B3&idcard=620122197809152318请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%88%98%E5%BB%B6%E8%8A%B3&idcard=620122197809152318
请求参数: {"name":"\u5218\u5ef6\u82b3","idcard":"620122197809152318"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************
请求参数: {"name":"\u674e\u6dd1\u7aef","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************
请求参数: {"name":"\u674e\u6dd1\u7aef","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%9D%8E%E6%B7%91%E7%AB%AF&idcard=******************
请求参数: {"name":"\u674e\u6dd1\u7aef","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718
请求参数: {"name":"\u5f20\u4e09","idcard":"320981200804052718"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042979
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042979"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E5%8D%8E%E4%B8%BD&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%BD%98%E5%8D%8E%E4%B8%BD&idcard=******************
请求参数: {"name":"\u6f58\u534e\u4e3d","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042972请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%AD%A6%E6%98%8E%E6%B6%9B&idcard=320981200805042972
请求参数: {"name":"\u6b66\u660e\u6d9b","idcard":"320981200805042972"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%A5%E7%B4%A0%E8%BF%90&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%B8%A5%E7%B4%A0%E8%BF%90&idcard=******************
请求参数: {"name":"\u4e25\u7d20\u8fd0","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718
请求参数: {"name":"\u5f20\u4e09","idcard":"320981200804052718"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BC%A0%E4%B8%89&idcard=320981200804052718
请求参数: {"name":"\u5f20\u4e09","idcard":"320981200804052718"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E5%8D%8E%E4%B8%BD&idcard=33900519910812672X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%91%A8%E5%8D%8E%E4%B8%BD&idcard=33900519910812672X
请求参数: {"name":"\u5468\u534e\u4e3d","idcard":"33900519910812672X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%8E%89%E5%B3%B0&idcard=341224200402054317请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E9%BB%84%E7%8E%89%E5%B3%B0&idcard=341224200402054317
请求参数: {"name":"\u9ec4\u7389\u5cf0","idcard":"341224200402054317"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639
请求参数: {"name":"\u5f90\u665f\u6d25","idcard":"330302200508307639"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E5%BE%90%E6%99%9F%E6%B4%A5&idcard=330302200508307639
请求参数: {"name":"\u5f90\u665f\u6d25","idcard":"330302200508307639"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%85%89%E8%8A%B9&idcard=533022200311162228请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E7%8E%8B%E5%85%89%E8%8A%B9&idcard=533022200311162228
请求参数: {"name":"\u738b\u5149\u82b9","idcard":"533022200311162228"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E9%AA%8F%E9%91%AB&idcard=342401200204038479请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%91%A3%E9%AA%8F%E9%91%AB&idcard=342401200204038479
请求参数: {"name":"\u8463\u9a8f\u946b","idcard":"342401200204038479"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E5%9B%BD%E4%B8%AD&idcard=51112919860205423X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BD%95%E5%9B%BD%E4%B8%AD&idcard=51112919860205423X
请求参数: {"name":"\u4f55\u56fd\u4e2d","idcard":"51112919860205423X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************
请求参数: {"name":"\u8def\u751c\u751c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E8%B7%AF%E7%94%9C%E7%94%9C&idcard=******************
请求参数: {"name":"\u8def\u751c\u751c","idcard":"******************"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%98%E5%A4%A9%E4%BF%8A&idcard=320381200906080090请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E4%BB%98%E5%A4%A9%E4%BF%8A&idcard=320381200906080090
请求参数: {"name":"\u4ed8\u5929\u4fca","idcard":"320381200906080090"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%A2%81%E4%BA%91%E9%AA%A5&idcard=44098119990627325X请求URL: http://app.nlc.cn/open/online/getSsoIdCard?name=%E6%A2%81%E4%BA%91%E9%AA%A5&idcard=44098119990627325X
请求参数: {"name":"\u6881\u4e91\u9aa5","idcard":"44098119990627325X"}
响应数据: {"message":"读者姓名和身份证信息不匹配，请重新输入","result":false}

